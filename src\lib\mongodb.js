import { MongoClient } from 'mongodb';

const uri = "mongodb+srv://jhon:<EMAIL>/?retryWrites=true&w=majority&appName=LA-LEGENDARIA-GERMAYORI";
const options = {};

let client;
let clientPromise;

if (process.env.NODE_ENV === 'development') {
  // En desarrollo, usa una variable global para preservar el valor
  // a través de module reloads causados por HMR (Hot Module Replacement).
  if (!global._mongoClientPromise) {
    client = new MongoClient(uri, options);
    global._mongoClientPromise = client.connect();
  }
  clientPromise = global._mongoClientPromise;
} else {
  // En producción, es mejor no usar una variable global.
  client = new MongoClient(uri, options);
  clientPromise = client.connect();
}

// Exportar una función para obtener la base de datos
export async function connectToDatabase() {
  try {
    const client = await clientPromise;
    const db = client.db('legendaria-germayori');
    return { client, db };
  } catch (error) {
    console.error('Error conectando a MongoDB:', error);
    throw error;
  }
}

// Exportar el clientPromise por defecto para compatibilidad
export default clientPromise;
