import { connectToDatabase } from '../../lib/mongodb';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Método no permitido' });
  }

  try {
    const { nombre, email, telefono, edad, pais } = req.body;

    // Validar datos requeridos
    if (!nombre || !email || !telefono || !edad || !pais) {
      return res.status(400).json({
        success: false,
        message: 'Todos los campos son requeridos'
      });
    }

    // Conectar a MongoDB
    const { db } = await connectToDatabase();
    const collection = db.collection('usuarios');

    // Verificar si el email ya existe
    const usuarioExistente = await collection.findOne({ correo: email });
    if (usuarioExistente) {
      return res.status(400).json({ 
        success: false, 
        message: 'Este email ya está registrado' 
      });
    }

    // Crear fechas
    const fechaActual = new Date();
    const fechaExpiracion = new Date();
    fechaExpiracion.setDate(fechaActual.getDate() + 30); // 30 días de acceso

    // Crear nuevo usuario
    const nuevoUsuario = {
      nombre: nombre.trim(),
      correo: email.toLowerCase().trim(),
      celular: telefono.trim(),
      edad: parseInt(edad),
      pais: pais,
      fechaRegistro: fechaActual.toISOString(),
      plan: "PREMIUM",
      estado: "activo",
      fechaExpiracion: fechaExpiracion.toISOString()
    };

    // Insertar en MongoDB
    const resultado = await collection.insertOne(nuevoUsuario);

    if (resultado.insertedId) {
      return res.status(201).json({ 
        success: true, 
        message: 'Usuario registrado exitosamente',
        userId: resultado.insertedId,
        fechaExpiracion: fechaExpiracion.toISOString()
      });
    } else {
      throw new Error('No se pudo insertar el usuario');
    }

  } catch (error) {
    console.error('❌ Error en registro:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error interno del servidor',
      error: error.message 
    });
  }
}
