import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [mensaje, setMensaje] = useState('');
  const router = useRouter();

  // Verificar si ya está logueado
  useEffect(() => {
    const usuarioLogueado = localStorage.getItem('usuario_germayori');
    if (usuarioLogueado) {
      router.push('/dashboard');
    }
  }, [router]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMensaje('');

    try {
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      });

      const data = await response.json();

      if (data.success) {
        // Guardar datos del usuario en localStorage
        localStorage.setItem('usuario_germayori', JSON.stringify(data.usuario));
        
        setMensaje('¡Login exitoso! Redirigiendo...');
        
        // Redireccionar al dashboard
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
        
      } else {
        setMensaje(data.message);
      }
    } catch (error) {
      console.error('Error:', error);
      setMensaje('Error de conexión. Intenta de nuevo.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Iniciar Sesión - GERMAYORI Academia</title>
        <meta name="description" content="Accede a tu cuenta de GERMAYORI Academia" />
      </Head>

      <div className="min-h-screen flex items-center justify-center px-6" style={{
        background: `
          linear-gradient(135deg, rgba(30, 58, 138, 0.9), rgba(67, 56, 202, 0.9)),
          radial-gradient(circle at 20% 20%, rgba(255, 165, 0, 0.2) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 69, 0, 0.2) 0%, transparent 50%),
          linear-gradient(45deg, #1e3a8a 0%, #3730a3 50%, #1e1b4b 100%)
        `
      }}>
        <div className="max-w-md w-full">
          {/* Logo y título */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <img
                src="/logo.png"
                alt="GERMAYORI"
                className="w-20 h-20 rounded-full border-4 border-orange-400 shadow-lg"
              />
            </div>
            <h1 className="text-4xl font-bold text-white mb-2">
              🚀 GERMAYORI
            </h1>
            <p className="text-orange-200 text-lg">
              Accede a tu Academia VIP
            </p>
          </div>

          {/* Formulario de login */}
          <div className="bg-white rounded-2xl p-8 shadow-2xl">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              Iniciar Sesión
            </h2>

            <form onSubmit={handleLogin} className="space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  Email de Registro
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none text-lg"
                  placeholder="<EMAIL>"
                  required
                  disabled={loading}
                />
                <p className="text-sm text-gray-600 mt-2">
                  Usa el mismo email con el que te registraste
                </p>
              </div>

              <button
                type="submit"
                disabled={loading}
                className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-4 rounded-lg font-bold text-lg transition-all transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? '🔄 Verificando...' : '🚀 Acceder a la Academia'}
              </button>
            </form>

            {/* Mensaje de estado */}
            {mensaje && (
              <div className={`mt-6 p-4 rounded-lg text-center font-semibold ${
                mensaje.includes('exitoso') 
                  ? 'bg-green-100 text-green-800 border border-green-300' 
                  : 'bg-red-100 text-red-800 border border-red-300'
              }`}>
                {mensaje}
              </div>
            )}

            {/* Enlaces adicionales */}
            <div className="mt-8 text-center space-y-4">
              <div className="border-t border-gray-200 pt-6">
                <p className="text-gray-600 mb-4">¿No tienes cuenta?</p>
                <a
                  href="/"
                  className="inline-block bg-gray-100 hover:bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold transition-all"
                >
                  📝 Registrarse y Pagar
                </a>
              </div>
              
              <div className="text-sm text-gray-500">
                <p>¿Problemas para acceder?</p>
                <a
                  href="https://chat.whatsapp.com/L4OdlXIE4Kx3av3TSQVOS6"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-green-600 hover:text-green-700 font-semibold"
                >
                  💬 Contactar Soporte WhatsApp
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
